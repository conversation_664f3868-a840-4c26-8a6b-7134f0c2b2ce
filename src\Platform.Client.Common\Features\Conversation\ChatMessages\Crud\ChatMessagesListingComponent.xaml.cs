using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Services;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using MobileApp.MauiShared;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Windows.Input;
namespace Platform.Client.Common.Features.Conversation;
public class ChatMessagesListingViewBase : CrudBaseMaui<IChatItem, ChatMessagesListingBusinessObject,
                                           ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService,
                                           ChatMessageFormBusinessObject, ChatMessageFormViewModel, string, IChatMessageFormDataService>
{
    public ChatMessagesListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}

public partial class ChatMessagesListingView : ChatMessagesListingViewBase
{
    private readonly string conversationId;
    private readonly ISecureKeyManager secureKeyManager;

    public ICommand BackCommand => new Command(async () => await Navigation.PopAsync());

    public override int RowsPerPage { get => 5; }
    public override bool LoadItemsOnEveryAppear => false;
    bool firstTime = true;
    public string FriendName { get; }
    private CancellationTokenSource? _hideCts;


    public ChatMessagesListingView(IServiceScopeFactory scopeFactory, string conversationId,
        string friendName, string friendAvatar) : base(scopeFactory)
    {
        this.conversationId = conversationId;
        FriendName = friendName;

        // Get encryption services from DI container
        var scope = scopeFactory.CreateScope();
        secureKeyManager = scope.ServiceProvider.GetRequiredService<ISecureKeyManager>();

        InitializeComponent();
        BindingContext = this;
        FilterViewModel.ConversationId = conversationId;
    }

    protected override IChatItem[] ConvertListingBusinessItemsToListingViewModelItems(List<ChatMessagesListingBusinessObject?> listBusinessObjects)
    {
        var orderedItems = new List<IChatItem>();
        DateTime lastDate = DateTime.MinValue;
        foreach (var item in listBusinessObjects.OrderBy(x => x.Timestamp))
        {
            if (lastDate.Date != item.Timestamp.GetValueOrDefault().Date)
            {
                orderedItems.Add(new DateSeparatorItem(item.Timestamp.GetValueOrDefault()));
                lastDate = item.Timestamp.GetValueOrDefault();
            }
            orderedItems.Add(new ChatMessagesListingViewModel()
            {
                Id = item.Id,
                Content = DecryptMessageContent(item.Content),
                IsIncoming = item.IsIncoming,
                DeliveryStatus = item.DeliveryStatus,
                Timestamp = item.Timestamp
            });
        }
        return orderedItems.ToArray();
    }

    /// <summary>
    /// Decrypts message content using RSA private key from secure memory
    /// Matches the exact implementation from MessagesListing.razor.cs
    /// </summary>
    private string DecryptMessageContent(string? encryptedContent)
    {
        if (string.IsNullOrEmpty(encryptedContent))
            return string.Empty;

        try
        {
            // Check if RSA key is available in memory
            if (!secureKeyManager.IsRSAKeyAvailable())
            {
                // If no key available, return placeholder indicating authentication needed
                return "[Authentication required to decrypt message]";
            }

            // Get RSA private key from secure memory
            using var privateKey = secureKeyManager.GetRSAPrivateKeyAsync();
            if (privateKey == null)
            {
                return "[Private Key Not Available]";
            }

            // Decrypt the message content
            var encryptedBytes = Convert.FromBase64String(encryptedContent);
            var decryptedBytes = privateKey.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
            return Encoding.UTF8.GetString(decryptedBytes);

        }
        catch (Exception ex)
        {
            // Log the error (in production, use proper logging)
            System.Diagnostics.Debug.WriteLine($"Failed to decrypt message: {ex.Message}");
            return "[Failed to decrypt message]";
        }
    }

    protected override void OnAppearing()
    {
        Shell.SetTabBarIsVisible(this, false);
        var signalRClientService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<SignalRClientService>();
        if (firstTime)
        {
            firstTime = false;
            PubSub.Hub.Default.Subscribe<string>((m) =>
            {
                if (m == "NewMessageReceived")
                {
                    // Reload items when a new message is received
                    _ = LoadItems(false);
                }
            });
            WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, async (r, m) =>
            {
                var item = Items.First(x => x.Id == m.Id);
                var chatMessage = item as ChatMessagesListingViewModel;
                if (chatMessage.DeliveryStatus != m.DeliveryStatus)
                {
                    chatMessage.DeliveryStatus = m.DeliveryStatus;
                    chatMessage.Timestamp = m.Timestamp;
                    await signalRClientService.AcknowledgeMessageRead(chatMessage.Id, DateTime.UtcNow);
                    chatMessage.DeliveryStatus = DeliveryStatus.ReadByEndUser;
                }

            });
        }

        base.OnAppearing();

        if (Items.Count == 0)
            return;
        collection.ScrollTo(Items.Count - 1);
    }


    public override async Task OnAfterSaveAsync(string key)
    {
        SelectedItem.Content = null;
        await LoadItems(false);

    }

    public override void UpdateItems(PagedDataList<ChatMessagesListingBusinessObject> pagedItems,
        IChatItem[] viewModelItems)
    {
        foreach (var item in viewModelItems)
        {
            if (!Items.Any(x => x.Id == item.Id))
            {
                Items.Add(item);
            }

            //if(Items.Any(x => x.Id == item.Id && x.ContentHash != item.ContentHash && x is ChatMessagesListingViewModel msg))
            //{
            //    var existingItem = Items.FirstOrDefault(x => x.ContentHash == item.ContentHash && x is ChatMessagesListingViewModel);
            //    if (existingItem is ChatMessagesListingViewModel existingMsg)
            //    {
            //        existingMsg.DeliveryStatus = ((ChatMessagesListingViewModel)item).DeliveryStatus;
            //        existingMsg.Timestamp = ((ChatMessagesListingViewModel)item).Timestamp;
            //        existingMsg.Content = ((ChatMessagesListingViewModel)item).Content;
            //    }
            //}
        }
    }

    protected override async Task ItemsLoaded(IChatMessagesListingDataService service)
    {
        collection.ScrollTo(Items.Count - 1);
        var pendingUpdates = Items.Where(x => x is ChatMessagesListingViewModel message
        && message.DeliveryStatus != DeliveryStatus.ReadByEndUser
        && message.IsIncoming);
        if (pendingUpdates.Any())
        {
            var scope = ScopeFactory.CreateScope();
            var signalRClientService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
            foreach (ChatMessagesListingViewModel item in pendingUpdates)
            {
                try
                {
                    await signalRClientService.AcknowledgeMessageRead(item.Id, DateTime.UtcNow);
                    item.DeliveryStatus = DeliveryStatus.ReadByEndUser;
                }
                catch (Exception ex)
                {
                    // Handle the exception, e.g., log it
                    Console.WriteLine($"Error acknowledging message read: {ex.Message}");
                }
            }
        }
    }

    protected override Task<ChatMessageFormViewModel> CreateSelectedItem()
    {
        return Task.FromResult(new ChatMessageFormViewModel()
        {
            ConversationId = conversationId
        });
    }

    private void CollectionView_SizeChanged(object sender, EventArgs e)
    {
        if (Items.Count == 0)
            return;

        collection.ScrollTo(Items.Count - 1);
    }



    void OnCollectionViewScrolled(object sender, ItemsViewScrolledEventArgs e)
    {
        // e.FirstVisibleItemIndex is the flat index in ItemsList
        var vm = BindingContext as ChatMessagesListingView;
        var idx = e.FirstVisibleItemIndex;
        if (vm == null || idx < 0 || idx >= vm.Items.Count)
            return;

        // pick the date to show
        string dateText;
        var item = vm.Items[idx];
        if (item is DateSeparatorItem ds)
            dateText = ds.Date.ToString("dd MMM yyyy");
        else if (item is ChatMessagesListingViewModel msg)
            dateText = msg.Timestamp.GetValueOrDefault().Date.ToString("dd MMM yyyy");
        else
            return;

        lblDateOverlay.Text = dateText;
        borderDateOverlay.IsVisible = true;

        // reset the hide timer
        _hideCts?.Cancel();
        _hideCts = new CancellationTokenSource();
        _ = HideOverlayAfterDelayAsync(_hideCts.Token);
    }

    async Task HideOverlayAfterDelayAsync(CancellationToken token)
    {
        try
        {
            await Task.Delay(1000, token);
            borderDateOverlay.IsVisible = false;
        }
        catch (TaskCanceledException) { }
    }
}

public class ChatTemplateSelector : DataTemplateSelector
{
    public DataTemplate MessageTemplate { get; set; }
    public DataTemplate DateSeparatorTemplate { get; set; }

    protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
    {
        return item switch
        {
            DateSeparatorItem => DateSeparatorTemplate,
            ChatMessagesListingViewModel => MessageTemplate,
            _ => MessageTemplate
        };
    }
}
// To fix the CS0311 error, the type 'IChatItem' must implement 'IEquatable<IChatItem>'.
// Adding the implementation to the 'IChatItem' interface and its relevant classes.




﻿using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Platform.Client.Services.Features.Conversation;
using Platform.Framework.Core;
using System.Text.Json;
using System.Threading.Channels;
using Message = DeepMessage.Client.Common.Data.Message;

namespace DeepMessage.MauiApp.Services;
public class SignalRClientService : IAsyncDisposable
{
    private HubConnection? _hubConnection;
    private CancellationTokenSource _cts = new CancellationTokenSource();

    // These dependencies should be resolved via DI.
    private IServiceScopeFactory? _scopeFactory;
    private ILogger<SignalRClientService> _logger = null!;
    Task? _monitoringTask;
    Task? _monitoringProducerTask;

    string _chatHubUrl = string.Empty;

    public SignalRClientService(ILogger<SignalRClientService> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    public void Start(string chatHubUrl)
    {
        _chatHubUrl = chatHubUrl;

        //_syncDownTask = Task.Factory.StartNew(StartDownSyncTask, TaskCreationOptions.RunContinuationsAsynchronously);
        if (_monitoringTask == null || _monitoringTask.Status != TaskStatus.Running)
        {
            _monitoringTask = Task.Factory.StartNew(StartMonitor, TaskCreationOptions.RunContinuationsAsynchronously);
            _monitoringProducerTask = Task.Factory.StartNew(() =>
            {
                while (!_cts.IsCancellationRequested)
                {
                    try
                    {
                        _monitoringChannel.Writer.TryWrite(string.Empty);
                        _logger.LogDebug("Monitoring task running...");
                        Thread.Sleep(15000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in monitoring task: {Error}", ex.Message);
                    }
                }
            });
        }
        //_downSyncChannel.Writer.WriteAsync(new ChatSyncItem() { Id = string.Empty, SyncType = 1 }, _cts.Token);

    }

    private async Task StartMonitor()
    {
        while (!_cts.IsCancellationRequested)
        {
            var hint = await _monitoringChannel.Reader.ReadAsync();
            await StartSignalRConnection(_cts.Token);
            await Task.Delay(5000);
        }
    }

    Channel<string> _monitoringChannel = Channel.CreateBounded<string>(10);

    private bool IsAlive(object? obj)
    {
        try
        {
            if (obj != null && obj.ToString() != null)
            {
                _logger.LogDebug("IsAlive: true");
                return true;
            }
            _logger.LogDebug("IsAlive: false");
            return false;
        }
        catch (Exception ex)
        {

            _logger.LogError("IsAlive error: {Error}", ex.Message);
            return false;
        }
    }

    private async Task StartSignalRConnection(CancellationToken token)
    {
        try
        {
            _logger.LogDebug("SignalR Starting connection...");
            using var scope = _scopeFactory!.CreateScope();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            var httpHandler = new HttpClientHandler
            {
                // WARNING: Do not use this in production.
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            if (_hubConnection != null && IsAlive(_hubConnection) && _hubConnection?.State == HubConnectionState.Disconnected)
            {
                _logger.LogInformation("Stopping existing SignalR connection...");
                try
                {
                    await _hubConnection?.StopAsync();
                    _hubConnection?.DisposeAsync();
                    _hubConnection = null;
                }
                catch { }

            }


            if (_hubConnection == null || !IsAlive(_hubConnection))
            {

                _logger.LogDebug("SignalR Building connection...");
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(_chatHubUrl, options =>
                    {
                        options.HttpMessageHandlerFactory = _ => httpHandler;
                        options.AccessTokenProvider = async () => await localStorageService.GetValue("auth_token");
                    })
                    .WithAutomaticReconnect(new[]
                    {
                    TimeSpan.Zero,
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(30)
                    })
                    .Build();

                _hubConnection.On<string, string>("OnNewFeed", ProcessIncomingMessage);

                _hubConnection.On("Logout", async () =>
                {
                    _logger.LogInformation("SignalR Logout event received.");
                    var scope = _scopeFactory.CreateScope();
                    var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                    await storageService.RemoveValue("auth_token");

                    //WeakReferenceMessenger.Default.Send("Logout");
                });


                _hubConnection.On<string, string>("OnFeedUpdate", UpdateMessage);

                _hubConnection.Reconnecting += error =>
                {
                    _logger?.LogWarning("SignalR reconnecting: {Error}", error?.Message);
                    return Task.CompletedTask;
                };

                _hubConnection.Reconnected += connectionId =>
                {
                    _logger?.LogInformation("SignalR reconnected. ConnectionId: {ConnectionId}", connectionId);
                    return Task.CompletedTask;
                };

                _hubConnection.Closed += async error =>
                {
                    _logger?.LogError("SignalR closed: {Error}", error?.Message);
                    await Task.Delay(TimeSpan.FromSeconds(5), token);
                    await _monitoringChannel.Writer.WriteAsync(string.Empty, token);
                };

                await _hubConnection.StartAsync(token);
                _logger.LogInformation("SignalR connection started...");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex.ToString());
            await _monitoringChannel.Writer.WriteAsync(string.Empty);
        }
    }

    // Process incoming SignalR messages.
    private async Task ProcessIncomingMessage(string userId, string messageJson)
    {
        try
        {
            _logger.LogInformation("SignalR Received broadcast: {0}", messageJson);
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessagesSyncFormBusinessObject>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {
                // Create a new scope for the DbContext to keep it short-lived.
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    message = new Message();
                    message.Id = formBusinessObject.Id;
                    message.ConversationId = formBusinessObject.ConversationId;
                    message.CreatedAt = formBusinessObject.CreatedAt;
                    message.SenderId = formBusinessObject.SenderId;
                    dbContext.Messages.Add(message);
                }

                message.DeletedAt = formBusinessObject.DeletedAt;
                message.DisappearAfter = formBusinessObject.DisappearAfter;
                message.DisappearAt = formBusinessObject.DisappearAt;
                message.IsDeleted = formBusinessObject.IsDeleted;
                message.IsEdited = formBusinessObject.IsEdited;
                message.IsEphemeral = formBusinessObject.IsEphemeral;
                message.EditedAt = formBusinessObject.EditedAt;
                await dbContext.SaveChangesAsync();

                foreach (var messageRecipient in formBusinessObject.MessageRecipients)
                {
                    var recipient = await dbContext.MessageRecipients.FirstOrDefaultAsync(x => x.Id == messageRecipient.Id);
                    if (recipient == null)
                    {
                        recipient = new MessageRecipient
                        {
                            Id = messageRecipient.Id,
                            MessageId = message.Id,
                            RecipientId = messageRecipient.RecipientId
                        };
                        dbContext.MessageRecipients.Add(recipient);
                    }

                    recipient.EncryptedContent = messageRecipient.EncryptedContent;
                    recipient.DeliveryStatus = messageRecipient.DeliveryStatus;
                }

                await dbContext.SaveChangesAsync();

                if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                    await _hubConnection.SendAsync("AcknowledgeMessage", message.Id, DeliveryStatus.DeliveredToEndUser, DateTime.UtcNow);

                _logger.LogDebug("Added message: {0}", message.Id);
                PubSub.Hub.Default.Publish("NewMessageReceived");

            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error processing broadcast: {Error}", ex.Message);
        }
    }

    /// <summary>
    /// Acknowledges message read with enhanced error handling and retry logic
    /// ✅ FIXED: Proper error handling, retry logic, and thread safety
    /// </summary>
    public async Task AcknowledgeMessageRead(string messageId, DateTime timeStamp)
    {
        const int maxRetries = 3;
        var retryCount = 0;

        while (retryCount < maxRetries)
        {
            try
            {
                if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                {
                    _logger.LogDebug("Acknowledging message read: {MessageId} (attempt {Attempt})", messageId, retryCount + 1);

                    // ✅ FIXED: Send acknowledgment with timeout
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    await _hubConnection.SendAsync("AcknowledgeMessage", messageId, DeliveryStatus.ReadByEndUser, timeStamp, cts.Token);

                    // ✅ FIXED: Update local database with proper error handling
                    using var scope = _scopeFactory.CreateScope();
                    var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                    var rowsAffected = await dbContext.Messages.Where(x => x.Id == messageId)
                        .ExecuteUpdateAsync(m => m.SetProperty(x => x.DeliveryStatus, DeliveryStatus.ReadByEndUser)
                                                 .SetProperty(x => x.DeliveryStatusTime, timeStamp));

                    if (rowsAffected > 0)
                    {
                        _logger.LogDebug("Successfully acknowledged message read: {MessageId}", messageId);
                        return; // Success - exit retry loop
                    }
                    else
                    {
                        _logger.LogWarning("No rows updated for message acknowledgment: {MessageId}", messageId);
                    }
                }
                else
                {
                    _logger.LogWarning("SignalR not connected - queueing acknowledgment for later: {MessageId}", messageId);
                    // ✅ FIXED: Queue for retry when connection is restored
                    // TODO: Implement acknowledgment queue for offline scenarios
                    return;
                }

                break; // Success - exit retry loop
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Timeout acknowledging message read: {MessageId} (attempt {Attempt})", messageId, retryCount + 1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acknowledging message read: {MessageId} (attempt {Attempt})", messageId, retryCount + 1);
            }

            retryCount++;
            if (retryCount < maxRetries)
            {
                var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount)); // Exponential backoff
                await Task.Delay(delay);
            }
        }

        if (retryCount >= maxRetries)
        {
            _logger.LogError("Failed to acknowledge message read after {MaxRetries} attempts: {MessageId}", maxRetries, messageId);
        }
    }

    // Down-sync task: e.g. process messages to retrieve full details.
    //private async Task StartDownSyncTask()
    //{
    //    while (!_cts.IsCancellationRequested)
    //    {
    //        try
    //        {
    //            var syncItem = await _downSyncChannel.Reader.ReadAsync(_cts.Token);
    //            using var scope = _scopeFactory!.CreateScope();
    //            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    //            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
    //            if (syncItem.SyncType == 0)
    //            {

    //                var conversations = await (from c in context.Conversations
    //                                           where c.SyncStatus == 100
    //                                           select c).ToArrayAsync();

    //                if (!conversations.Any())
    //                    continue;

    //                var chatThreadSyncService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
    //                foreach (var conversation in conversations)
    //                {
    //                    var formBusinessObject = await chatThreadSyncService.GetItemByIdAsync(conversation.Id);

    //                    conversation.CreatedAt = formBusinessObject.CreatedAt;
    //                    conversation.IsDeleted = formBusinessObject.IsDeleted;
    //                    conversation.Type = formBusinessObject.Type;
    //                    conversation.Title = formBusinessObject.Title;
    //                    await context.SaveChangesAsync();

    //                    ArgumentNullException.ThrowIfNull(formBusinessObject.ChatParticipents);

    //                    foreach (var item in formBusinessObject.ChatParticipents)
    //                    {
    //                        var participent = await context.ConversationParticipants.FirstOrDefaultAsync(x => x.Id == item.Id);
    //                        if (participent == null)
    //                        {
    //                            participent = new ConversationParticipant()
    //                            {
    //                                Id = item.Id,
    //                                ConversationId = item.ConversationId,
    //                                IsAdmin = item.IsAdmin,
    //                                JoinedAt = item.JoinedAt,
    //                                UserId = item.UserId,
    //                            };
    //                            context.ConversationParticipants.Add(participent);
    //                            await context.SaveChangesAsync();
    //                        }
    //                    }

    //                    conversation.SyncStatus = 101;
    //                    await context.SaveChangesAsync();

    //                }
    //            }
    //            if (syncItem.SyncType == 1)
    //            {
    //                var messages = await (from m in context.Messages
    //                                      where m.SyncStatus == 100
    //                                      select m).ToListAsync();

    //                if (!messages.Any())
    //                    continue;

    //                var chatMessagSyncService = scope.ServiceProvider.GetRequiredService<IChatMessagesSyncFormDataService>();

    //                foreach (var message in messages)
    //                {
    //                    var formBusinessObject = await chatMessagSyncService.GetItemByIdAsync(message.Id);
    //                    message.Id = formBusinessObject.Id;
    //                    message.ConversationId = formBusinessObject.ConversationId;
    //                    message.CreatedAt = formBusinessObject.CreatedAt;
    //                    message.DeletedAt = formBusinessObject.DeletedAt;
    //                    message.DisappearAfter = formBusinessObject.DisappearAfter;
    //                    message.DisappearAt = formBusinessObject.DisappearAt;
    //                    message.IsDeleted = formBusinessObject.IsDeleted;
    //                    message.IsEdited = formBusinessObject.IsEdited;
    //                    message.IsEphemeral = formBusinessObject.IsEphemeral;
    //                    message.EditedAt = formBusinessObject.EditedAt;
    //                    message.PlainContent = formBusinessObject.PlainContent;
    //                    message.SenderId = formBusinessObject.SenderId;
    //                    await context.SaveChangesAsync();

    //                    //todo: optimize for only required data
    //                    foreach (var participant in formBusinessObject.MessageRecipients)
    //                    {
    //                        var messageRecipient = await context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.Id);
    //                        if (messageRecipient == null)
    //                        {
    //                            messageRecipient = new MessageRecipient()
    //                            {
    //                                Id = participant.Id,
    //                                MessageId = participant.MessageId,
    //                                RecipientId = participant.RecipientId,
    //                                EncryptedContent = message.PlainContent!,
    //                            };
    //                        }
    //                        context.MessageRecipients.Add(messageRecipient);
    //                        await context.SaveChangesAsync();
    //                    }
    //                    message.SyncStatus = 101;
    //                    await context.SaveChangesAsync();
    //                }

    //                if (messages.Count > 0)
    //                {
    //                    if (IsAppInForeground())
    //                    {
    //                        await localStorageService.SetValue("true", "message");
    //                        var intent = new Intent("com.companyname.deepmessage.UPDATE");
    //                        intent.PutExtra("message", "New data available");
    //                        SendBroadcast(intent);
    //                    }
    //                    else
    //                    {
    //                        // App is in background, show a system notification.
    //                        ShowMessageNotification(messages.Count.ToString());
    //                    }
    //                }

    //            }
    //            // Process down-sync for chat messages.
    //            // (For example, download full message body and update local DB.)
    //            // You can call an injected sync service here.

    //            // After processing, you might also trigger UI updates, etc.
    //        }
    //        catch (System.OperationCanceledException) { break; }
    //        catch (Exception ex)
    //        {
    //            _logger?.LogWarning("Down-sync error: {Error}", ex.Message);
    //        }
    //    }
    //}


    public async Task UpdateMessage(string userId, string messageJson)
    {
        try
        {
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessageUpdate>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    throw new InvalidOperationException();
                }
                // ✅ FIXED: Thread-safe status update with optimistic concurrency
                var originalStatus = message.DeliveryStatus;
                var originalTimestamp = message.DeliveryStatusTime;

                message.DeliveryStatus = formBusinessObject.DeliveryStatus;
                message.DeliveryStatusTime = formBusinessObject.DeliveryStatusTime;

                try
                {
                    await dbContext.SaveChangesAsync();

                    WeakReferenceMessenger.Default.Send(new ChatMessageStatus()
                    {
                        Id = message.Id,
                        DeliveryStatus = message.DeliveryStatus,
                        Timestamp = message.DeliveryStatusTime
                    });

                    _logger?.LogDebug("Updated message {MessageId} status to {Status}", message.Id, message.DeliveryStatus);
                }
                catch (DbUpdateConcurrencyException)
                {
                    // ✅ FIXED: Handle concurrent updates gracefully
                    _logger?.LogWarning("Concurrent update detected for message {MessageId}, reloading", message.Id);
                    await dbContext.Entry(message).ReloadAsync();

                    // Only update if the new status is more recent
                    if (formBusinessObject.DeliveryStatusTime > message.DeliveryStatusTime)
                    {
                        message.DeliveryStatus = formBusinessObject.DeliveryStatus;
                        message.DeliveryStatusTime = formBusinessObject.DeliveryStatusTime;
                        await dbContext.SaveChangesAsync();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error updating message: {Error}", ex.Message);
        }
    }

    public async ValueTask DisposeAsync()
    {
        _cts.Cancel();
        if (_hubConnection != null)
        {
            await _hubConnection.StopAsync();
            await _hubConnection.DisposeAsync();
            _hubConnection = null;
        }
    }
}





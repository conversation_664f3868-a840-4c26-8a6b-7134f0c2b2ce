namespace MobileApp.MauiShared.Converters
{
    public class InvertBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return !((bool)value);
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean (IsIncoming) to IShape for chat bubble appearance
    /// </summary>
    public class BubbleShapeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isIncoming)
            {
                return isIncoming
                    ? new RoundRectangle { CornerRadius = new CornerRadius(0, 8, 8, 8) }
                    : new RoundRectangle { CornerRadius = new CornerRadius(8, 0, 8, 8) };
            }
            return new RoundRectangle { CornerRadius = new CornerRadius(8) };
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean (IsIncoming) to Color for chat bubble background
    /// </summary>
    public class BackgroundColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isIncoming)
            {
                return isIncoming ? Colors.White : Color.FromArgb("#E4E4E7");
            }
            return Colors.White;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean (IsIncoming) to LayoutOptions for chat bubble alignment
    /// </summary>
    public class HorizontalOptionsConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isIncoming)
            {
                return isIncoming ? LayoutOptions.Start : LayoutOptions.End;
            }
            return LayoutOptions.Start;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean (IsIncoming) to Color for message text
    /// </summary>
    public class MessageColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isIncoming)
            {
                return isIncoming ? Color.FromArgb("#333333") : Colors.Black;
            }
            return Colors.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean (IsIncoming) to Color for timestamp text
    /// </summary>
    public class TimestampColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isIncoming)
            {
                return isIncoming ? Color.FromArgb("#777777") : Color.FromArgb("#666666");
            }
            return Color.FromArgb("#777777");
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

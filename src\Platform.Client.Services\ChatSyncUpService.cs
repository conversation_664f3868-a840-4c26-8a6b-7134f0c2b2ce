﻿using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace DeepMessage.MauiApp.Services;

public class ChatSyncUpService
{
    private readonly IServiceScopeFactory scopeFactory;
    private readonly BaseHttpClient httpClient;
    private readonly ILogger<ChatSyncUpService> logger;

    private BlockingCollection<ChatSyncItem> SyncItems { get; set; }

    public void Sync(ChatSyncItem item)
    {
        SyncItems.Add(item);
    }

    public ChatSyncUpService(IServiceScopeFactory scopeFactory,
        BaseHttpClient httpClient,
        ILogger<ChatSyncUpService> logger)
    {
        this.scopeFactory = scopeFactory;
        this.httpClient = httpClient;
        this.logger = logger;
        SyncItems = [new ChatSyncItem() { Id = string.Empty, SyncType = 0 }, new ChatSyncItem() { Id = string.Empty, SyncType = 1 }];
    }
    private bool _keepRunning = true;

    private Task? _syncTask;
    private Task? _monitorTask;
    public void Start()
    {
        if (_monitorTask != null && _monitorTask.Status == TaskStatus.Running)
            return;

        _keepRunning = true;

        _syncTask = Task.Factory.StartNew(async () =>
        {
            while (_keepRunning)
            {
                var item = SyncItems.Take();
                try
                {

                    using var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    if (item.SyncType == 0)
                    {

                        var conversations = await (from c in context.Conversations
                                                   where c.SyncStatus == 0
                                                   select new ChatThreadSyncFormBusinessObject
                                                   {
                                                       Id = c.Id,
                                                       CreatedAt = c.CreatedAt,
                                                       IsDeleted = c.IsDeleted,
                                                       Title = c.Title,
                                                       Type = c.Type,
                                                       ChatParticipents = (from p in context.ConversationParticipants
                                                                           where c.Id == p.ConversationId
                                                                           select new ChatParticipentsSyncFormBusinessObject()
                                                                           {
                                                                               Id = p.Id,
                                                                               ConversationId = p.ConversationId,
                                                                               IsAdmin = p.IsAdmin,
                                                                               JoinedAt = p.JoinedAt,
                                                                               UserId = p.UserId,
                                                                           }).ToList()
                                                   }).ToArrayAsync();

                        foreach (var conversation in conversations)
                        {
                            var chatThreadSyncService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
                            await chatThreadSyncService.SaveAsync(conversation);
                            context.Conversations.Where(x => x.Id == conversation.Id)
                                .ExecuteUpdate(x => x.SetProperty(p => p.SyncStatus, 1));
                        }
                    }
                    if (item.SyncType == 1)
                    {
                        // ✅ SECURE: Query MessageRecipient table for E2E encrypted sync
                        var messages = await (from m in context.Messages
                                              where m.DeliveryStatus <= ServiceContracts.Enums.DeliveryStatus.QueuedToUpSync
                                              select new ChatMessagesSyncFormBusinessObject()
                                              {
                                                  Id = m.Id,
                                                  ConversationId = m.ConversationId,
                                                  CreatedAt = m.CreatedAt,
                                                  DeletedAt = m.DeletedAt,
                                                  DisappearAfter = m.DisappearAfter,
                                                  DisappearAt = m.DisappearAt,
                                                  EditedAt = m.EditedAt,
                                                  IsDeleted = m.IsDeleted,
                                                  IsEdited = m.IsEdited,
                                                  IsEphemeral = m.IsEphemeral,
                                                  PlainContent = null, // ✅ SECURE: Never sync plaintext
                                                  SenderId = m.SenderId,
                                                  // ✅ SECURE: Include encrypted MessageRecipient data
                                                  MessageRecipients = (from mr in context.MessageRecipients
                                                                       where mr.MessageId == m.Id
                                                                       select new MessageRecipientSyncFormBusinessObject
                                                                       {
                                                                           Id = mr.Id,
                                                                           MessageId = mr.MessageId,
                                                                           RecipientId = mr.RecipientId,
                                                                           EncryptedContent = mr.EncryptedContent,
                                                                           DeliveryStatus = mr.DeliveryStatus,
                                                                           DeliveryStatusTime = mr.ReadAt,
                                                                           IsRead = mr.IsRead,
                                                                           ReadAt = mr.ReadAt,
                                                                           SyncStatus = mr.SyncStatus
                                                                       }).ToList()
                                              }).ToListAsync();
                        logger.LogDebug("SyncUpService: Queued {0} messages to sync", messages.Count);
                        foreach (var message in messages)
                        {
                            try
                            {
                                var chatmessagesSyncService = scope.ServiceProvider.GetRequiredService<IChatMessagesSyncFormDataService>();
                                await chatmessagesSyncService.SaveAsync(message);

                                // ✅ SECURE: Update delivery status for Message and all MessageRecipients
                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.SentToMessageServer));

                                // ✅ SECURE: Update MessageRecipient delivery status
                                await context.MessageRecipients.Where(x => x.MessageId == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.SentToMessageServer));

                                logger.LogDebug("Successfully synced message {MessageId}", message.Id);
                            }
                            catch (HttpRequestException httpEx)
                            {
                                logger.LogWarning(httpEx, "Network error syncing message {MessageId} - will retry", message.Id);

                                // ✅ FIXED: Different handling for network vs other errors
                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.Pending));

                                await context.MessageRecipients.Where(x => x.MessageId == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.Pending));
                            }
                            catch (TaskCanceledException tcEx)
                            {
                                logger.LogWarning(tcEx, "Timeout syncing message {MessageId} - will retry", message.Id);

                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.Pending));

                                await context.MessageRecipients.Where(x => x.MessageId == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.Pending));
                            }
                            catch (Exception ex)
                            {
                                logger.LogError(ex, "Critical error syncing message {MessageId}", message.Id);

                                // ✅ FIXED: Mark as delivery failed for critical errors
                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.DeliveryFailed));

                                await context.MessageRecipients.Where(x => x.MessageId == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, ServiceContracts.Enums.DeliveryStatus.DeliveryFailed));
                            }
                        }

                        if (messages.Count > 0)
                        {
                            WeakReferenceMessenger.Default.Send(new ChatSyncItem { SyncType = 3 });
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                    await Task.Delay(30000);
                    SyncItems.Add(item);
                }

            }
        });


        _monitorTask = Task.Factory.StartNew(async () =>
        {
            while (_keepRunning)
            {
                try
                {
                    using var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var messages = await (from m in context.Messages
                                          where m.DeliveryStatus == ServiceContracts.Enums.DeliveryStatus.QueuedToUpSync
                                          select new ChatSyncItem()
                                          {
                                              Id = m.Id,
                                              SyncType = 1
                                          }).ToListAsync();
                    logger.LogDebug("SyncUpService: Periodic {0} messages to sync", messages.Count);
                    foreach (var message in messages)
                    {
                        Sync(message);
                    }

                    await Task.Delay(30000);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                    await Task.Delay(60000);
                }
            }
        });
    }

    public void Stop()
    {
        _keepRunning = false;
    }
}


public class ChatSyncItem
{
    public string Id { get; set; } = null!;


    /// <summary>
    /// 0 = Chat Thread
    /// 1 = Chat Message
    /// </summary>
    public int SyncType { get; set; }
}